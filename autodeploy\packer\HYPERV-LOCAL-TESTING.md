# Hyper-V Local Testing Guide

This guide helps you test your Packer build process locally using Hyper-V before deploying to VMware or AWS.

## 🎯 Purpose

- **Validate scripts** before expensive cloud builds
- **Test configurations** in a local environment
- **Debug issues** quickly without cloud resources
- **Ensure compatibility** across platforms

## 📋 Prerequisites

### 1. Hyper-V Requirements
```powershell
# Check if Hyper-V is enabled
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All

# Enable Hyper-V if needed (requires restart)
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
```

### 2. System Requirements
- **Windows 10 Pro/Enterprise** or **Windows Server**
- **8GB+ RAM** (4GB allocated to VM)
- **80GB+ free disk space**
- **Administrator privileges**

### 3. Software Requirements
- **Packer** installed and in PATH
- **Windows Server 2022 ISO** at `.\ISO\Server 2022\SERVER_EVAL_x64FRE_en-us.iso`

## 🚀 Quick Start

### 1. Verify Your Setup
```powershell
# Navigate to the packer directory
cd autodeploy/packer

# Validate the template
.\build-hyperv.ps1 -<PERSON>ida<PERSON>Only
```

### 2. Run Local Test Build
```powershell
# Full build with all scripts
.\build-hyperv.ps1

# Fast build (skip Windows Updates for testing)
.\build-hyperv.ps1 -SkipUpdates

# Debug build with detailed logging
.\build-hyperv.ps1 -Debug
```

### 3. Monitor Progress
- **Hyper-V Manager**: Watch VM creation and boot process
- **PowerShell**: Monitor script execution and output
- **Build time**: Expect 30-60 minutes for full build

## ⚙️ Configuration

### ISO Path
Update the ISO path in `variables/hyperv.pkrvars.hcl`:
```hcl
iso_path = "./ISO/Server 2022/SERVER_EVAL_x64FRE_en-us.iso"
```

### VM Specifications
Adjust VM resources as needed:
```hcl
vm_cpu_num  = 2      # Number of CPUs
vm_mem_size = 4096   # Memory in MB (4GB)
vm_disk_size = 61440 # Disk in MB (60GB)
```

### Hyper-V Switch
Ensure you have a virtual switch configured:
```hcl
switch_name = "Default Switch"  # Or your custom switch name
```

## 🔧 Customization

### Skip Specific Scripts
Edit `windows-server-2022/hyperv.pkr.hcl` to comment out scripts:
```hcl
# Comment out for faster testing
# provisioner "powershell" {
#   script = "scripts/windows-updates.ps1"
# }
```

### Test Individual Scripts
Run scripts manually on the VM:
```powershell
# Connect to the VM via PowerShell Direct
Enter-PSSession -VMName "windows-server-2022-local-test"

# Test your scripts
.\scripts\configure-windows-features.ps1
```

## 🐛 Troubleshooting

### Common Issues

#### Hyper-V Not Available
```powershell
# Error: Hyper-V feature not found
# Solution: Enable Hyper-V and restart
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
Restart-Computer
```

#### ISO Not Found
```powershell
# Error: ISO file not found
# Solution: Verify ISO path and file existence
Test-Path ".\ISO\Server 2022\SERVER_EVAL_x64FRE_en-us.iso"
```

#### VM Creation Fails
```powershell
# Check available virtual switches
Get-VMSwitch

# Create a virtual switch if needed
New-VMSwitch -Name "PackerSwitch" -SwitchType Internal
```

#### WinRM Connection Issues
```powershell
# Check VM network connectivity
Get-VM "windows-server-2022-local-test" | Get-VMNetworkAdapter

# Verify WinRM is running in the VM
Enter-PSSession -VMName "windows-server-2022-local-test"
Get-Service WinRM
```

### Debug Mode
Enable detailed logging:
```powershell
$env:PACKER_LOG = "1"
.\build-hyperv.ps1 -Debug
```

### Manual Cleanup
If builds fail, clean up manually:
```powershell
# Remove failed VM
Remove-VM "windows-server-2022-local-test" -Force

# Clean up VHD files
Remove-Item "C:\Users\<USER>\Documents\Hyper-V\Virtual hard disks\windows-server-2022-local-test.vhdx" -Force
```

## ✅ Validation Checklist

After a successful build, verify:

- [ ] VM boots successfully
- [ ] All software is installed
- [ ] Windows features are configured
- [ ] Security settings are applied
- [ ] Network connectivity works
- [ ] WinRM is properly configured

## 🔄 Next Steps

Once your local test passes:

1. **Update VMware variables** in `variables/vmware.pkrvars.hcl`
2. **Run VMware build**: `.\build-vmware.ps1 -Version 2022`
3. **Update AWS variables** in `variables/aws.pkrvars.hcl`
4. **Run AWS build**: `.\build-aws.ps1 -Version 2022`

## 📊 Performance Tips

### Faster Testing
- Use `-SkipUpdates` flag for script validation
- Comment out Windows Updates in the template
- Reduce VM memory/CPU for basic testing
- Use SSD storage for better performance

### Resource Management
- Close unnecessary applications
- Monitor disk space during builds
- Use Task Manager to monitor resource usage

## 🤝 Integration

The Hyper-V template uses the same:
- **Provisioning scripts** as VMware/AWS
- **Answer files** for unattended installation
- **Configuration patterns** for consistency

This ensures your local tests accurately reflect production builds.

---

**Ready to test locally? Run `.\build-hyperv.ps1` to get started!**
