# Linux Container Guide for Packer Builds

This guide shows how to use the Linux shell scripts and Docker container for cross-platform Packer builds.

## 🐧 Linux Shell Scripts

### Available Scripts

- `build-vmware.sh` - Build VMware templates
- `build-aws.sh` - Build AWS AMIs  
- `build-all.sh` - Build all platforms and versions

### Prerequisites

```bash
# Install Packer
curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
sudo apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
sudo apt-get update && sudo apt-get install packer

# Install AWS CLI (for AWS builds)
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure AWS credentials
aws configure
```

### Usage Examples

```bash
# Make scripts executable
chmod +x *.sh

# Build all platforms and versions
./build-all.sh

# Build only VMware templates
./build-all.sh --platform vmware

# Build only AWS AMIs
./build-all.sh --platform aws

# Build specific version with debug
./build-vmware.sh --version 2022 --debug

# Build in parallel for faster execution
./build-all.sh --parallel

# Get help for any script
./build-aws.sh --help
```

## 🐳 Docker Container Approach

### Quick Start

```bash
# Build the container
docker compose build

# Run interactive container
docker compose run --rm packer-builder bash

# Inside container, run builds
./build-all.sh --help
./build-aws.sh --version 2022
```

### Environment Configuration

Create a `.env` file:

```bash
# AWS Configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=us-east-1

# VMware Configuration
VCENTER_SERVER=your-vcenter.domain.com
VCENTER_USERNAME=<EMAIL>
VCENTER_PASSWORD=your-password

# Debug Configuration
PACKER_LOG=1
TZ=America/New_York
```

### Container Services

```bash
# Run AWS-specific builds
docker compose run --rm packer-aws ./build-aws.sh -v 2022

# Run VMware-specific builds  
docker compose run --rm packer-vmware ./build-vmware.sh -v 2019

# Run all builds in parallel
docker compose run --rm packer-builder ./build-all.sh -j

# Interactive development
docker compose run --rm packer-builder bash
```

## 🔧 Advanced Usage

### Custom Variable Files

```bash
# Use custom variable files
./build-vmware.sh --var-file variables/vmware-prod.pkrvars.hcl

# Override specific variables
./build-aws.sh --version 2022 --debug \
  --var-file variables/aws-staging.pkrvars.hcl
```

### Parallel Builds

```bash
# Build multiple versions in parallel
./build-all.sh --parallel --platform both --version both

# Monitor progress
tail -f logs/packer.log
```

### CI/CD Integration

```bash
# GitLab CI example
script:
  - docker compose run --rm packer-builder ./build-all.sh --platform aws

# GitHub Actions example
- name: Build AMIs
  run: |
    docker compose run --rm packer-builder \
      ./build-aws.sh --version 2022
```

## 📁 Directory Structure

```
autodeploy/packer/
├── build-vmware.sh          # VMware build script
├── build-aws.sh             # AWS build script  
├── build-all.sh             # Comprehensive build script
├── Dockerfile               # Container definition
├── docker-compose.yml       # Container orchestration
├── .env                     # Environment variables
├── logs/                    # Build logs
│   └── packer.log
├── variables/               # Variable files
│   ├── vmware.pkrvars.hcl
│   ├── aws.pkrvars.hcl
│   └── hyperv.pkrvars.hcl
└── windows-server-*/        # Packer templates
```

## 🚀 Benefits of Container Approach

### Consistency
- **Same environment** across development, staging, and production
- **Reproducible builds** regardless of host OS
- **Version-locked tools** (Packer, AWS CLI, etc.)

### Portability  
- **Run anywhere** Docker is available
- **No local dependencies** to install or manage
- **Easy CI/CD integration**

### Isolation
- **Clean environment** for each build
- **No conflicts** with host system tools
- **Secure credential handling**

## 🔍 Troubleshooting

### Container Issues

```bash
# Check container logs
docker compose logs packer-builder

# Debug container environment
docker compose run --rm packer-builder env

# Access container shell
docker compose run --rm packer-builder bash

# Rebuild container with latest changes
docker compose build --no-cache
```

### Script Issues

```bash
# Enable debug mode
export PACKER_LOG=1
./build-aws.sh --debug --version 2022

# Check script syntax
bash -n build-vmware.sh

# Run with verbose output
bash -x ./build-all.sh --platform vmware
```

### Permission Issues

```bash
# Fix script permissions
chmod +x *.sh

# Fix file ownership in container
docker compose run --rm --user root packer-builder \
  chown -R packer:packer /workspace
```

## 📊 Performance Tips

### Faster Builds
```bash
# Use parallel builds
./build-all.sh --parallel

# Skip Windows Updates for testing
# (Edit templates to comment out windows-updates.ps1)

# Use faster instance types in AWS
# (Edit aws.pkrvars.hcl: instance_type = "c5.xlarge")
```

### Resource Management
```bash
# Monitor container resources
docker stats packer-windows-builder

# Limit container resources
docker compose run --rm --memory=4g --cpus=2 packer-builder

# Clean up after builds
docker system prune -f
```

## 🔗 Integration Examples

### Jenkins Pipeline
```groovy
pipeline {
    agent any
    stages {
        stage('Build Images') {
            steps {
                sh 'docker compose run --rm packer-builder ./build-all.sh'
            }
        }
    }
}
```

### GitLab CI
```yaml
build-images:
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker compose run --rm packer-builder ./build-all.sh --platform aws
```

### GitHub Actions
```yaml
- name: Build Packer Images
  run: |
    docker compose run --rm packer-builder \
      ./build-all.sh --version 2022 --parallel
```

---

**Ready to build in containers? Run `docker compose up -d` to get started!**
