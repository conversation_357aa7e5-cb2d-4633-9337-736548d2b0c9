# Packer Configuration for Windows Server Images

This directory contains Packer templates for building Windows Server 2022 and 2019 images for both VMware and AWS deployment. The templates are designed to create standardized, hardened, and optimized Windows Server images that integrate with your existing AutoDeploy infrastructure.

## 🏗️ Structure
```
autodeploy/packer/
├── windows-server-2022/          # Windows Server 2022 templates
│   ├── vmware.pkr.hcl            # VMware vSphere template
│   ├── aws.pkr.hcl               # AWS AMI template
│   └── hyperv.pkr.hcl            # Hyper-V local testing template
├── windows-server-2019/          # Windows Server 2019 templates
│   ├── vmware.pkr.hcl            # VMware vSphere template
│   └── aws.pkr.hcl               # AWS AMI template
├── scripts/                      # Provisioning scripts
│   ├── setup/                    # Initial setup scripts
│   ├── windows-updates.ps1       # Windows Update installation
│   ├── install-vmware-tools.ps1  # VMware Tools installation
│   ├── install-aws-tools.ps1     # AWS tools and drivers
│   ├── configure-windows-features.ps1  # Windows features/roles
│   ├── install-common-software.ps1     # Common software packages
│   ├── security-hardening.ps1    # Security hardening measures
│   ├── aws-specific-config.ps1   # AWS-specific configurations
│   ├── cleanup.ps1               # System cleanup
│   └── sysprep.ps1               # System preparation
├── answer-files/                 # Unattended installation files
│   ├── windows-server-2022/      # Server 2022 answer files
│   └── windows-server-2019/      # Server 2019 answer files
├── variables/                    # Variable files
│   ├── vmware.pkrvars.hcl        # VMware environment variables
│   └── aws.pkrvars.hcl           # AWS environment variables
├── build-vmware.ps1              # VMware build script
├── build-aws.ps1                 # AWS build script
├── build-hyperv.ps1              # Hyper-V local testing script
├── build-all.ps1                 # Comprehensive build script
├── HYPERV-LOCAL-TESTING.md       # Hyper-V testing guide
└── README.md                     # This file
```

## 🚀 Quick Start

### 1. Prerequisites
- **Packer** installed and in PATH
- **VMware vSphere access** (for VMware builds)
- **AWS credentials configured** (for AWS builds)
- **Windows Server ISO files** available (for VMware/Hyper-V builds)
- **Hyper-V enabled** (for local testing)

### 2. Configure Variables
Copy and customize the variable files:

```powershell
# For VMware builds
cp variables/vmware.pkrvars.hcl variables/vmware-prod.pkrvars.hcl
# Edit vmware-prod.pkrvars.hcl with your environment details

# For AWS builds
cp variables/aws.pkrvars.hcl variables/aws-prod.pkrvars.hcl
# Edit aws-prod.pkrvars.hcl with your AWS settings
```

### 3. Build Images

#### Option A: Use the automated build scripts (Recommended)
```powershell
# Build all images (VMware + AWS, both versions)
.\build-all.ps1

# Build only VMware templates
.\build-all.ps1 -Platform vmware

# Build only AWS AMIs
.\build-all.ps1 -Platform aws

# Build only Windows Server 2022
.\build-all.ps1 -Version 2022

# Build with debug logging
.\build-all.ps1 -Debug

# Build in parallel (faster but more resource intensive)
.\build-all.ps1 -Parallel
```

#### Option B: Use platform-specific scripts
```powershell
# VMware builds
.\build-vmware.ps1 -Version both    # Build both 2019 and 2022
.\build-vmware.ps1 -Version 2022    # Build only 2022
.\build-vmware.ps1 -Version 2019    # Build only 2019

# AWS builds
.\build-aws.ps1 -Version both       # Build both 2019 and 2022
.\build-aws.ps1 -Version 2022       # Build only 2022
.\build-aws.ps1 -Version 2019       # Build only 2019
```

#### Option C: Manual Packer commands
```bash
# VMware builds
packer build -var-file="variables/vmware.pkrvars.hcl" windows-server-2022/vmware.pkr.hcl
packer build -var-file="variables/vmware.pkrvars.hcl" windows-server-2019/vmware.pkr.hcl

# AWS builds
packer build -var-file="variables/aws.pkrvars.hcl" windows-server-2022/aws.pkr.hcl
packer build -var-file="variables/aws.pkrvars.hcl" windows-server-2019/aws.pkr.hcl
```

## ⚙️ Configuration

### VMware Configuration
Edit `variables/vmware.pkrvars.hcl` with your environment details:

```hcl
# vCenter connection details
vcenter_server   = "your-vcenter-server.domain.com"
vcenter_username = "<EMAIL>"
vcenter_password = "your-password"

# vSphere infrastructure
datacenter = "Your-Datacenter"
cluster    = "Your-Cluster"
datastore  = "Your-Datastore"
network    = "Your-Network-PortGroup"
folder     = "Templates"

# ISO paths
iso_path_2022 = "[Your-Datastore] ISO/Windows_Server_2022.iso"
iso_path_2019 = "[Your-Datastore] ISO/Windows_Server_2019.iso"
```

### AWS Configuration
Edit `variables/aws.pkrvars.hcl` with your AWS settings:

```hcl
# AWS credentials and region
aws_region = "us-east-1"  # Change to your preferred region

# Network configuration (optional)
vpc_id            = "vpc-xxxxxxxxx"     # Your VPC ID
subnet_id         = "subnet-xxxxxxxxx"  # Your subnet ID
security_group_id = "sg-xxxxxxxxx"      # Your security group ID

# Instance configuration
instance_type = "t3.large"  # Suitable for Windows builds
```

## 🔧 Customization

### Adding Custom Software
To add custom software to your images, modify the `scripts/install-common-software.ps1` file:

```powershell
# Add your custom software packages
$customSoftware = @(
    "your-custom-package",
    "another-package"
)

foreach ($package in $customSoftware) {
    choco install $package -y --no-progress
}
```

### Modifying Security Settings
Customize security hardening in `scripts/security-hardening.ps1`:

```powershell
# Add your organization's security requirements
# Example: Configure additional audit policies
auditpol /set /category:"Object Access" /success:enable /failure:enable
```

### Environment-Specific Configurations
Create environment-specific variable files:

```powershell
# Development environment
cp variables/vmware.pkrvars.hcl variables/vmware-dev.pkrvars.hcl

# Production environment
cp variables/vmware.pkrvars.hcl variables/vmware-prod.pkrvars.hcl

# Use with builds
.\build-vmware.ps1 -VarFile "variables/vmware-dev.pkrvars.hcl"
```

## 🔍 What's Included

### Base Configuration
- ✅ Windows Updates installed
- ✅ .NET Framework 4.8
- ✅ PowerShell 5.1+
- ✅ Common Windows features enabled
- ✅ Security hardening applied
- ✅ System optimizations

### VMware Images Include
- ✅ VMware Tools
- ✅ VMware-optimized drivers
- ✅ vSphere integration

### AWS Images Include
- ✅ AWS CLI v2
- ✅ AWS PowerShell modules
- ✅ CloudWatch Agent
- ✅ SSM Agent
- ✅ EC2 Instance Connect
- ✅ AWS-optimized drivers

### Common Software
- ✅ 7-Zip
- ✅ Notepad++
- ✅ Google Chrome
- ✅ Firefox
- ✅ PuTTY
- ✅ WinSCP
- ✅ Sysinternals Suite
- ✅ Git
- ✅ Visual Studio Code
- ✅ Visual C++ Redistributables

### Security Hardening
- ✅ UAC configured
- ✅ Windows Firewall enabled
- ✅ SMBv1 disabled
- ✅ Unnecessary services disabled
- ✅ Password policy configured
- ✅ Audit policies enabled
- ✅ Windows Defender configured

## 🔗 Integration with Existing AutoDeploy

These Packer templates are designed to integrate seamlessly with your existing AutoDeploy infrastructure:

### VMware Integration
The generated templates can be used directly with your existing `VMwareDeploy-latest.ps1` script:

```powershell
# In your deployment script, reference the new templates
$template = "windows-server-2022-template"  # Generated by Packer
```

### Domain Integration
The images are pre-configured to work with your domain joining process:

```powershell
# Your existing New-ADComputer-latest.ps1 script will work unchanged
# The images support the same customization specs and domain joining
```

### Automation Compatibility
- ✅ Compatible with existing PowerShell modules
- ✅ Supports existing credential management
- ✅ Works with current job management system
- ✅ Maintains existing logging and monitoring

## 🚨 Troubleshooting

### Common Issues

#### VMware Builds
```powershell
# Issue: vCenter connection fails
# Solution: Check credentials and network connectivity
packer validate -var-file="variables/vmware.pkrvars.hcl" windows-server-2022/vmware.pkr.hcl

# Issue: ISO not found
# Solution: Verify ISO path in variables file
# Ensure ISO is uploaded to datastore
```

#### AWS Builds
```powershell
# Issue: AWS credentials not configured
# Solution: Configure AWS CLI
aws configure

# Issue: VPC/Subnet not found
# Solution: Update variables file with correct IDs
aws ec2 describe-vpcs
aws ec2 describe-subnets
```

#### General Issues
```powershell
# Enable debug logging
$env:PACKER_LOG = "1"
packer build -var-file="variables/your-vars.pkrvars.hcl" template.pkr.hcl

# Validate template syntax
packer validate template.pkr.hcl

# Check for required variables
packer inspect template.pkr.hcl
```

### Build Times
- **VMware builds**: 45-90 minutes per template
- **AWS builds**: 30-60 minutes per AMI
- **Parallel builds**: Can reduce total time by 40-60%

### Resource Requirements
- **VMware**: 4GB RAM, 2 vCPUs during build
- **AWS**: t3.large instance (2 vCPUs, 8GB RAM)
- **Disk space**: 60GB+ per image

## 📋 Next Steps

1. **Test the Images**
   ```powershell
   # VMware: Deploy a test VM from the template
   # AWS: Launch a test EC2 instance from the AMI
   ```

2. **Update Terraform Configurations**
   ```hcl
   # Update your Terraform configs to use the new images
   # VMware: Reference the new template names
   # AWS: Reference the new AMI IDs
   ```

3. **Integrate with CI/CD**
   ```powershell
   # Add Packer builds to your CI/CD pipeline
   # Schedule regular image updates
   # Automate testing of new images
   ```

4. **Monitor and Maintain**
   ```powershell
   # Set up monitoring for image builds
   # Schedule monthly image updates
   # Track image usage and performance
   ```

## 🤝 Contributing

To contribute improvements to these templates:

1. Test changes in a development environment
2. Update documentation as needed
3. Ensure compatibility with existing AutoDeploy scripts
4. Follow the established naming conventions

## 📞 Support

For issues related to:
- **Packer templates**: Check the troubleshooting section above
- **VMware integration**: Verify vSphere permissions and connectivity
- **AWS integration**: Ensure proper IAM permissions and VPC configuration
- **AutoDeploy compatibility**: Test with existing deployment scripts

---

**Built with ❤️ for automated infrastructure deployment**
