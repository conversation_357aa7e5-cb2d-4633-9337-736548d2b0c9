# Build AWS AMI Script
# This script builds both Windows Server 2019 and 2022 AWS AMIs

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("2019", "2022", "both")]
    [string]$Version = "both",
    
    [Parameter(Mandatory=$false)]
    [string]$VarFile = "variables/aws.pkrvars.hcl",
    
    [Parameter(Mandatory=$false)]
    [switch]$Debug
)

Write-Host "Building AWS Windows Server AMIs" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Check if Packer is installed
try {
    $packerVersion = packer version
    Write-Host "Packer version: $packerVersion" -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: Packer is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check AWS credentials
Write-Host "Checking AWS credentials..." -ForegroundColor Cyan
try {
    aws sts get-caller-identity | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "AWS CLI command failed"
    }
    Write-Host "AWS credentials verified" -ForegroundColor Green
} catch {
    Write-Host "ERROR: AWS credentials not configured or AWS CLI not available" -ForegroundColor Red
    Write-Host "Please configure AWS credentials using 'aws configure' or environment variables" -ForegroundColor Yellow
    exit 1
}

# Check if variable file exists
if (-not (Test-Path $VarFile)) {
    Write-Host "ERROR: Variable file '$VarFile' not found" -ForegroundColor Red
    Write-Host "Please create the variable file or specify a different one with -VarFile parameter" -ForegroundColor Yellow
    exit 1
}

# Set Packer log level if debug is enabled
if ($Debug) {
    $env:PACKER_LOG = "1"
    Write-Host "Debug logging enabled" -ForegroundColor Yellow
}

# Function to build an AMI
function Build-AMI {
    param(
        [string]$TemplatePath,
        [string]$VersionName
    )
    
    Write-Host "`nBuilding Windows Server $VersionName AWS AMI..." -ForegroundColor Yellow
    Write-Host "Template: $TemplatePath" -ForegroundColor Cyan
    Write-Host "Variables: $VarFile" -ForegroundColor Cyan
    
    $startTime = Get-Date
    
    try {
        # Validate the template first
        Write-Host "Validating template..." -ForegroundColor Cyan
        packer validate -var-file="$VarFile" "$TemplatePath"
        
        if ($LASTEXITCODE -ne 0) {
            throw "Template validation failed"
        }
        
        Write-Host "Template validation successful" -ForegroundColor Green
        
        # Build the AMI
        Write-Host "Starting build..." -ForegroundColor Cyan
        packer build -var-file="$VarFile" "$TemplatePath"
        
        if ($LASTEXITCODE -ne 0) {
            throw "AMI build failed"
        }
        
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        Write-Host "Windows Server $VersionName AWS AMI built successfully!" -ForegroundColor Green
        Write-Host "Build duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor Cyan
        
        # Try to get the AMI ID from the output
        Write-Host "Checking for created AMI..." -ForegroundColor Cyan
        
    } catch {
        Write-Host "ERROR: Failed to build Windows Server $VersionName AMI: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Build AMIs based on version parameter
$buildResults = @()

if ($Version -eq "2019" -or $Version -eq "both") {
    $result = Build-AMI -TemplatePath "windows-server-2019/aws.pkr.hcl" -VersionName "2019"
    $buildResults += @{ Version = "2019"; Success = $result }
}

if ($Version -eq "2022" -or $Version -eq "both") {
    $result = Build-AMI -TemplatePath "windows-server-2022/aws.pkr.hcl" -VersionName "2022"
    $buildResults += @{ Version = "2022"; Success = $result }
}

# Summary
Write-Host "`n" -ForegroundColor Green
Write-Host "Build Summary" -ForegroundColor Green
Write-Host "=============" -ForegroundColor Green

$successCount = 0
$totalCount = $buildResults.Count

foreach ($result in $buildResults) {
    $status = if ($result.Success) { "SUCCESS" } else { "FAILED" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    
    Write-Host "Windows Server $($result.Version): $status" -ForegroundColor $color
    
    if ($result.Success) {
        $successCount++
    }
}

Write-Host "`nTotal: $successCount/$totalCount builds successful" -ForegroundColor Cyan

if ($successCount -eq $totalCount) {
    Write-Host "All builds completed successfully!" -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Yellow
    Write-Host "1. Check your AWS console for the new AMIs" -ForegroundColor White
    Write-Host "2. Test the AMIs by launching EC2 instances" -ForegroundColor White
    Write-Host "3. Update your Terraform configurations to use the new AMI IDs" -ForegroundColor White
    exit 0
} else {
    Write-Host "Some builds failed. Check the output above for details." -ForegroundColor Red
    exit 1
}
