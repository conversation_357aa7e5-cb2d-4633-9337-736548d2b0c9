#!/bin/bash
# Build VMware Templates Script
# This script builds both Windows Server 2019 and 2022 VMware templates

set -euo pipefail

# Default values
VERSION="both"
VAR_FILE="variables/vmware.pkrvars.hcl"
DEBUG_MODE=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -v, --version VERSION    Version to build (2019, 2022, both) [default: both]"
    echo "  -f, --var-file FILE      Variable file to use [default: variables/vmware.pkrvars.hcl]"
    echo "  -d, --debug              Enable debug logging"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                       # Build both versions"
    echo "  $0 -v 2022              # Build only Windows Server 2022"
    echo "  $0 -v 2019 -d          # Build Windows Server 2019 with debug"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            if [[ ! "$VERSION" =~ ^(2019|2022|both)$ ]]; then
                print_color $RED "ERROR: Version must be 2019, 2022, or both"
                exit 1
            fi
            shift 2
            ;;
        -f|--var-file)
            VAR_FILE="$2"
            shift 2
            ;;
        -d|--debug)
            DEBUG_MODE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_color $RED "ERROR: Unknown option $1"
            show_usage
            exit 1
            ;;
    esac
done

print_color $GREEN "Building VMware Windows Server Templates"
print_color $GREEN "======================================="

# Check if Packer is installed
if ! command -v packer &> /dev/null; then
    print_color $RED "ERROR: Packer is not installed or not in PATH"
    print_color $YELLOW "Please install Packer from: https://www.packer.io/downloads"
    exit 1
fi

PACKER_VERSION=$(packer version)
print_color $CYAN "Packer version: $PACKER_VERSION"

# Check if variable file exists
if [[ ! -f "$VAR_FILE" ]]; then
    print_color $RED "ERROR: Variable file '$VAR_FILE' not found"
    print_color $YELLOW "Please create the variable file or specify a different one with -f parameter"
    exit 1
fi

# Set Packer log level if debug is enabled
if [[ "$DEBUG_MODE" == true ]]; then
    export PACKER_LOG=1
    print_color $YELLOW "Debug logging enabled"
fi

# Function to build a template
build_template() {
    local template_path=$1
    local version_name=$2
    
    print_color $CYAN "Building Windows Server $version_name VMware template..."
    print_color $CYAN "Template: $template_path"
    print_color $CYAN "Variables: $VAR_FILE"
    
    local start_time=$(date +%s)
    
    # Validate the template first
    print_color $CYAN "Validating template..."
    if ! packer validate -var-file="$VAR_FILE" "$template_path"; then
        print_color $RED "ERROR: Template validation failed"
        return 1
    fi
    
    print_color $GREEN "Template validation successful"
    
    # Build the template
    print_color $CYAN "Starting build..."
    if ! packer build -var-file="$VAR_FILE" "$template_path"; then
        print_color $RED "ERROR: Template build failed"
        return 1
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local hours=$((duration / 3600))
    local minutes=$(((duration % 3600) / 60))
    local seconds=$((duration % 60))
    
    print_color $GREEN "Windows Server $version_name VMware template built successfully!"
    printf "${CYAN}Build duration: %02d:%02d:%02d${NC}\n" $hours $minutes $seconds
    
    return 0
}

# Build templates based on version parameter
declare -a build_results=()

if [[ "$VERSION" == "2019" || "$VERSION" == "both" ]]; then
    if build_template "windows-server-2019/vmware.pkr.hcl" "2019"; then
        build_results+=("2019:SUCCESS")
    else
        build_results+=("2019:FAILED")
    fi
fi

if [[ "$VERSION" == "2022" || "$VERSION" == "both" ]]; then
    if build_template "windows-server-2022/vmware.pkr.hcl" "2022"; then
        build_results+=("2022:SUCCESS")
    else
        build_results+=("2022:FAILED")
    fi
fi

# Display final results
echo ""
print_color $GREEN "========================================="
print_color $GREEN "BUILD SUMMARY"
print_color $GREEN "========================================="

success_count=0
total_count=${#build_results[@]}

for result in "${build_results[@]}"; do
    IFS=':' read -r version status <<< "$result"
    if [[ "$status" == "SUCCESS" ]]; then
        print_color $GREEN "✅ Windows Server $version: $status"
        ((success_count++))
    else
        print_color $RED "❌ Windows Server $version: $status"
    fi
done

echo ""
if [[ $success_count -eq $total_count ]]; then
    print_color $GREEN "🎉 All builds completed successfully! ($success_count/$total_count)"
    exit 0
else
    print_color $RED "⚠️  Some builds failed. ($success_count/$total_count successful)"
    exit 1
fi
