version: '3.8'

services:
  packer-builder:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: packer-windows-builder
    environment:
      # AWS Configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
      
      # Azure Configuration (if needed)
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
      - AZURE_SUBSCRIPTION_ID=${AZURE_SUBSCRIPTION_ID}
      
      # VMware Configuration
      - VCENTER_SERVER=${VCENTER_SERVER}
      - VCENTER_USERNAME=${VCENTER_USERNAME}
      - VCENTER_PASSWORD=${VCENTER_PASSWORD}
      
      # Packer Configuration
      - PACKER_LOG=${PACKER_LOG:-0}
      - PACKER_LOG_PATH=/workspace/logs/packer.log
      
      # Timezone
      - TZ=${TZ:-UTC}
    
    volumes:
      # Mount the current directory for development
      - .:/workspace
      
      # Mount logs directory
      - ./logs:/workspace/logs
      
      # Mount AWS credentials (alternative to environment variables)
      - ~/.aws:/home/<USER>/.aws:ro
      
      # Mount SSH keys for VMware access
      - ~/.ssh:/home/<USER>/.ssh:ro
      
      # Mount any ISO files
      - ./ISO:/workspace/ISO:ro
    
    working_dir: /workspace
    
    # Keep container running for interactive use
    tty: true
    stdin_open: true
    
    # Network configuration
    network_mode: host
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Optional: Separate service for AWS builds only
  packer-aws:
    extends: packer-builder
    container_name: packer-aws-builder
    environment:
      - BUILD_PLATFORM=aws
    command: ["./build-aws.sh", "--help"]

  # Optional: Separate service for VMware builds only  
  packer-vmware:
    extends: packer-builder
    container_name: packer-vmware-builder
    environment:
      - BUILD_PLATFORM=vmware
    command: ["./build-vmware.sh", "--help"]

# Optional: Create a custom network
networks:
  packer-network:
    driver: bridge

# Volumes for persistent data
volumes:
  packer-logs:
    driver: local
