version: '3.8'

services:
  # Main Packer builder service
  packer-builder:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - PACKER_VERSION=${PACKER_VERSION:-1.10.0}
        - TERRAFORM_VERSION=${TERRAFORM_VERSION:-1.6.6}
    container_name: packer-windows-builder
    hostname: packer-builder

    environment:
      # AWS Configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
      - AWS_PROFILE=${AWS_PROFILE:-default}

      # Azure Configuration (optional)
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
      - AZURE_SUBSCRIPTION_ID=${AZURE_SUBSCRIPTION_ID}

      # VMware vSphere Configuration
      - VCENTER_SERVER=${VCENTER_SERVER}
      - VCENTER_USERNAME=${VCENTER_USERNAME}
      - VCENTER_PASSWORD=${VCENTER_PASSWORD}
      - VCENTER_DATACENTER=${VCENTER_DATACENTER}
      - VCENTER_CLUSTER=${VCENTER_CLUSTER}
      - VCENTER_DATASTORE=${VCENTER_DATASTORE}
      - VCENTER_NETWORK=${VCENTER_NETWORK}

      # Packer Configuration
      - PACKER_LOG=${PACKER_LOG:-0}
      - PACKER_LOG_PATH=/workspace/logs/packer.log
      - PACKER_CACHE_DIR=/workspace/packer_cache

      # Build Configuration
      - BUILD_PLATFORM=${BUILD_PLATFORM:-both}
      - BUILD_VERSION=${BUILD_VERSION:-both}
      - BUILD_DEBUG=${BUILD_DEBUG:-false}
      - BUILD_PARALLEL=${BUILD_PARALLEL:-false}

      # System Configuration
      - TZ=${TZ:-UTC}
      - DEBIAN_FRONTEND=noninteractive

    volumes:
      # Mount the current directory for development
      - .:/workspace

      # Mount logs directory (create if doesn't exist)
      - ./logs:/workspace/logs

      # Mount packer cache for faster builds
      - packer-cache:/workspace/packer_cache

      # Mount AWS credentials (alternative to environment variables)
      - ${HOME}/.aws:/home/<USER>/.aws:ro

      # Mount SSH keys for VMware access
      - ${HOME}/.ssh:/home/<USER>/.ssh:ro

      # Mount any ISO files (create directory if needed)
      - ${ISO_PATH:-./ISO}:/workspace/ISO:ro

      # Mount Docker socket for potential nested container builds
      - /var/run/docker.sock:/var/run/docker.sock:ro

    working_dir: /workspace

    # Keep container running for interactive use
    tty: true
    stdin_open: true

    # Network configuration - use host network for VMware access
    network_mode: ${NETWORK_MODE:-host}

    # Resource limits
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-4G}
          cpus: ${CPU_LIMIT:-2.0}
        reservations:
          memory: ${MEMORY_RESERVATION:-2G}
          cpus: ${CPU_RESERVATION:-1.0}

    # Health check
    healthcheck:
      test: ["CMD", "packer", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

    # Restart policy
    restart: unless-stopped

  # AWS-specific builder service
  packer-aws:
    extends: packer-builder
    container_name: packer-aws-builder
    environment:
      - BUILD_PLATFORM=aws
    command: ["bash", "-c", "echo 'AWS Packer Builder Ready. Run: docker compose exec packer-aws ./build-aws.sh --help'"]
    profiles:
      - aws-only

  # VMware-specific builder service
  packer-vmware:
    extends: packer-builder
    container_name: packer-vmware-builder
    environment:
      - BUILD_PLATFORM=vmware
    command: ["bash", "-c", "echo 'VMware Packer Builder Ready. Run: docker compose exec packer-vmware ./build-vmware.sh --help'"]
    profiles:
      - vmware-only

  # Development service with additional tools
  packer-dev:
    extends: packer-builder
    container_name: packer-dev-builder
    volumes:
      # Additional development mounts
      - .:/workspace
      - ./dev-tools:/workspace/dev-tools
    environment:
      - PACKER_LOG=1
      - BUILD_DEBUG=true
    command: ["bash"]
    profiles:
      - development

  # CI/CD service for automated builds
  packer-ci:
    extends: packer-builder
    container_name: packer-ci-builder
    environment:
      - CI=true
      - BUILD_PARALLEL=true
      - PACKER_LOG=1
    volumes:
      # Minimal mounts for CI
      - .:/workspace:ro
      - ./logs:/workspace/logs
      - ci-cache:/workspace/packer_cache
    command: ["./build-all.sh", "--parallel"]
    profiles:
      - ci

# Networks
networks:
  packer-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes for persistent data
volumes:
  packer-cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${CACHE_PATH:-./packer_cache}

  packer-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOGS_PATH:-./logs}

  ci-cache:
    driver: local
